"use client"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Play,
  Pause,
  Heart,
  Calendar,
  Music,
  Award,
  ExternalLink,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { MusicPlayer } from "../../../components/shared/music-player/music-player"
import { useMusicPlayer } from "@/contexts/music-player-context/music-player-context"
import { SONG_QUERY } from "@/graphql/queries"
import { useQuery } from '@apollo/client'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"



export default function SongPage({ songId }: { songId?: string }) {
  const router = useRouter();
  const { playSong, currentSong, isPlaying, togglePlay } = useMusicPlayer();

  // Apollo Client query
  const { data: apolloData, loading, error } = useQuery(SONG_QUERY, {
    variables: { songId },
    skip: !songId, // Skip query if no songId
  });

  // Process Apollo data from connection structure
  const songConnection = apolloData?.songsConnection || null;
  const songDetails = songConnection?.edges?.[0]?.node || null;

  const handlePlayClick = () => {
    if (!songDetails) return;
    const firstRecording = songDetails.recordings?.[0];
    if (currentSong?.id === songDetails.mbid) {
      togglePlay();
    } else {
      playSong({
        id: songDetails.mbid || "",
        title: songDetails.title ?? "",
        artist: (songDetails.creditedOnSong?.[0]?.name || songDetails.creditedOnSong?.[0]?.artistName) ?? "Unknown Artist",
        album: "",
        albumArt: (songDetails.coverImage || firstRecording?.coverImage) ?? "/dummy-image.png",
        duration: firstRecording?.duration ?? 0,
        audioSrc: "",
        credits: {} as { producer?: string; writer?: string; engineer?: string },
      });
    }
  };

  // const handleArtistClick = () => {
  //   router.push(`/artist`);
  // };
    const handleRecordingClick = (id: string | undefined) => {
      if(id){
        router.push(`/recording/?id=${id}`);
      }
    };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="px-4 md:px-8 lg:px-16 py-8">
          {/* Header / Hero Section Skeleton */}
          <div className="flex flex-col lg:flex-row gap-8 mb-8">
            {/* Album Cover Skeleton */}
            <div className="flex-shrink-0">
              <Skeleton className="w-[300px] h-[300px] rounded-lg" />
            </div>
            {/* Song Info Skeleton */}
            <div className="flex-1 space-y-6">
              <div className="space-y-4">
                <Skeleton className="h-12 w-2/3" />
              </div>
              {/* Action Buttons Skeleton */}
              <div className="flex flex-wrap gap-3">
                <Skeleton className="h-12 w-28" />
                <Skeleton className="h-12 w-28" />
              </div>
              {/* Song Metadata Skeleton */}
              <div className="flex flex-wrap items-center gap-6">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-24" />
                <Skeleton className="h-6 w-28" />
              </div>
            </div>
          </div>

          {/* Recordings and Credits Skeleton */}
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Recordings Skeleton */}
            <Card className="flex-1">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold flex items-center gap-2">
                  <Music className="w-5 h-5" />
                  <Skeleton className="h-6 w-32" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center gap-6 py-2">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-5 w-20" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                ))}
              </CardContent>
            </Card>
            {/* Credits Skeleton */}
            <Card className="flex-1">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  <Skeleton className="h-6 w-24" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 h-[25rem] overflow-y-auto">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between py-2">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  if (error) {
    return <div className="p-8 text-center text-red-500">Error loading song: {error.message}</div>;
  }

  if (!songDetails) {
    return <div className="p-8 text-center text-red-500">Song not found</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header / Hero Section */}
        <div className="flex flex-col lg:flex-row gap-8 mb-8">
          {/* Album Cover */}
          <div className="flex-shrink-0">
            <Image
              src={(songDetails?.coverImage || songDetails?.recordings?.[0]?.coverImage) ?? "/dummy-image.png"}
              alt="Song Cover"
              width={300}
              height={300}
              className="rounded-lg shadow-lg"
            />
          </div>

          {/* Song Info */}
          <div className="flex-1 space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl font-bold">{songDetails?.title ?? "Unknown Title"}</h1>
              <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{songDetails?.recordings?.[0]?.releaseDate ?? "Unknown"}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Music className="w-4 h-4" />
                  <span>
                    {songDetails?.recordings?.[0]?.duration
                      ? `${Math.floor((songDetails.recordings[0].duration / 1000) / 60)}:${String(
                          Math.floor((songDetails.recordings[0].duration / 1000) % 60)
                        ).padStart(2, "0")}`
                      : "Unknown"}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <Button size="lg" className="gap-2" onClick={handlePlayClick}>
                {currentSong?.id === songDetails?.id && isPlaying ? (
                  <>
                    <Pause className="w-5 h-5" />
                    Pause
                  </>
                ) : (
                  <>
                    <Play className="w-5 h-5" />
                    Play
                  </>
                )}
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Heart className="w-5 h-5" />
                Like
              </Button>
              {/* <Button variant="outline" size="lg" className="gap-2">
                <Plus className="w-5 h-5" />
                Add to Playlist
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Edit3 className="w-5 h-5" />
                Edit
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Share2 className="w-5 h-5" />
                Share
              </Button> */}
            </div>

            {/* Song Metadata */}
            <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
              {/* <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>
                  {songDetails?.duration
                    ? `${Math.floor(songDetails.duration / 60)}:${String(songDetails.duration % 60).padStart(2, "0")}`
                    : "N/A"}
                </span>
              </div> */}
              {/* <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{songDetails?.releaseDate ?? "Unknown Date"}</span>
              </div> */}
              {/* <div className="flex items-center gap-2">
                <Music className="w-4 h-4" />
                <span>ISRC: {songDetails?.recordID ?? "N/A"}</span>
              </div> */}
            </div>

            {/* Genres */}
            {/* <div className="flex flex-wrap gap-2">
              <Badge variant="secondary">Synthwave</Badge>
              <Badge variant="secondary">Pop</Badge>
              <Badge variant="secondary">Electronic</Badge>
            </div> */}
          </div>
        </div>

        {/* Recordings and Credits Row/Column Responsive */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Recordings */}
          <Card className="flex-1">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-semibold flex items-center gap-2">
                <Music className="w-5 h-5" />
                Recordings
              </CardTitle>
            </CardHeader>
            <CardContent
              className={
                songDetails?.recordings && songDetails.recordings.length > 0
                  ? "space-y-6 "
                  : "flex flex-col justify-center items-center min-h-[12rem]"
              }
            >
              {songDetails?.recordings && songDetails.recordings.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Release Date</TableHead>
                      {/* <TableHead>Actions</TableHead> */}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {(songDetails.recordings || []).map((rec: {
                      mbid?: string;
                      title?: string;
                      duration?: number;
                      releaseDate?: string;
                    }) => (
                      <TableRow key={rec?.mbid}>
                        <TableCell className="cursor-pointer" onClick={()=>handleRecordingClick(rec?.mbid)}>{rec?.title ?? "Untitled"}</TableCell>
                        <TableCell>
                          {rec?.duration
                            ? `${Math.floor(rec?.duration / 60000)}:${String(Math.floor((rec?.duration % 60000) / 1000)).padStart(2, "0")}`
                            : "N/A"}
                        </TableCell>
                        <TableCell>{rec?.releaseDate ?? "N/A"}</TableCell>
                        {/* <TableCell>
                          <Button size="sm" onClick={() => {}}>
                            <Play className="w-4 h-4" />
                          </Button>
                        </TableCell> */}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center w-full">
                  <Music className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No recordings available</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Credits */}
          <Card className="flex-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5" />
                Credits
              </CardTitle>
            </CardHeader>
            <CardContent
              className={
                songDetails?.creditedOnSongConnection?.edges?.length > 0
                  ? "space-y-4 h-[25rem] overflow-y-auto"
                  : "flex flex-col justify-center items-center min-h-[12rem]"
              }
            >
              <div className="grid gap-4 w-full">
                {songDetails?.creditedOnSongConnection?.edges?.length > 0 ? (
                  (() => {
                    type Role = { role: string; as?: string };
                    type Person = {
                      mbid?: string;
                      name: string;
                      profileImage?: string;
                      roles: Role[];
                      links?: string;
                    };
                    type CreditEdge = {
                      role: string;
                      as?: string;
                      node: {
                        mbid?: string;
                        name?: string;
                        artistName?: string;
                        profileImage?: string;
                        links?: string;
                      };
                    };
                    const grouped: Record<string, Person> = (songDetails.creditedOnSongConnection.edges || []).reduce(
                      (acc: Record<string, Person>, edge: CreditEdge) => {
                        const person = edge.node;
                        const name = person?.name || person?.artistName || "Unknown Artist";
                        if (!acc[name]) {
                          acc[name] = {
                            mbid: person?.mbid,
                            name,
                            profileImage: person?.profileImage,
                            roles: [],
                            links: person?.links,
                          };
                        }
                        acc[name].roles.push({ role: edge.role, as: edge.as });
                        return acc;
                      },
                      {}
                    );
                    return Object.values(grouped).map((person, idx) => (
                      <div key={person.mbid ?? idx} className="flex items-start gap-4 p-2 rounded-lg">
                        {person.profileImage && person.profileImage.trim() ? (
                          <Image
                            src={person.profileImage.trim().startsWith('http') ? person.profileImage.trim() : `/${person.profileImage.trim()}`}
                            alt={person.name}
                            width={48}
                            height={48}
                            className="rounded-full object-cover border"
                          />
                        ) : (
                          <Avatar className="w-12 h-12">
                            <AvatarFallback className="text-sm font-medium">
                              {person.name
                                .split(' ')
                                .map(word => word.charAt(0).toUpperCase())
                                .slice(0, 2)
                                .join('')}
                            </AvatarFallback>
                          </Avatar>
                        )}
                        <div className="flex-1">
                          <p
                            className="font-medium text-primary cursor-pointer hover:underline"
                            onClick={() => router.push(`/artist/?id=${person.mbid}`)}
                          >
                            {person.name}
                          </p>
                          <ul className="text-sm text-muted-foreground capitalize list-disc ml-4">
                            {person.roles.map((r, i) => (
                              <li key={i}>{r.role}{r.as ? ` (${r.as})` : ""}</li>
                            ))}
                          </ul>
                        </div>
                        <Button variant="ghost" size="sm" onClick={() => router.push(`/artist/?id=${person.mbid}`)}>
                          <ExternalLink className="w-4 h-4" />
                        </Button>
                      </div>
                    ));
                  })()
                ) : (
                  <div className="text-muted-foreground text-sm text-center w-full">No credits available</div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <MusicPlayer />
    </div>
  )
}
