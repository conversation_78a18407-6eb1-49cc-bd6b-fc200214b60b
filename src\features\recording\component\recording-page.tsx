"use client"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Play,
  Pause,
  Clock,
  Calendar,
  Music,
  Award,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { MusicPlayer } from "../../../components/shared/music-player/music-player"
import { useMusicPlayer } from "@/contexts/music-player-context/music-player-context"
import { RECORDING_QUERY } from "@/graphql/queries"
import { useQuery } from '@apollo/client'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"

// Define performer type at the top
interface Performer {
  mbid?: string;
  name?: string;
  artistName?: string;
  profileImage?: string;
  links?: string;
  role?: string;
  as?: string;
}

export default function RecordingPage({ recordingId }: { recordingId?: string }) {
  const router = useRouter();
  const { playSong, currentSong, isPlaying, togglePlay } = useMusicPlayer();

  // Apollo Client query
  const { data: apolloData, loading, error } = useQuery(RECORDING_QUERY, {
    variables: { recordingId },
    skip: !recordingId, // Skip query if no recordingId
  });

  // Process Apollo data from connection structure
  const recordingConnection = apolloData?.recordingsConnection || null;
  const recordingDetails = recordingConnection?.edges?.[0]?.node || null;

  // Extract data from the new connection structure
  const rec = recordingDetails || null;
  const song = recordingDetails?.song || null;

  // Process credited artists from connection structure
  const performedBy: Performer[] = recordingDetails?.creditedOnRecordingConnection?.edges?.map((edge: {
    role?: string;
    as?: string;
    node?: {
      mbid?: string;
      name?: string;
      artistName?: string;
      profileImage?: string;
      links?: string;
    };
  }) => ({
    ...edge.node,
    role: edge.role,
    as: edge.as,
  })) || [];

  // Deduplicate performers by mbid or name/artistName
  const uniquePerformedBy: Performer[] = performedBy.filter((artist, idx, arr) => {
    const key = artist.mbid || artist.name || artist.artistName;
    return arr.findIndex(a => (a.mbid || a.name || a.artistName) === key) === idx;
  });

  // Process song credits from connection structure
  const songCredits = song?.recordings?.[0]?.songConnection?.edges?.[0]?.node?.creditedOnSongConnection?.edges?.map((edge: {
    role?: string;
    as?: string;
    node?: {
      mbid?: string;
      name?: string;
      artistName?: string;
      profileImage?: string;
      links?: string;
    };
  }) => ({
    ...edge.node,
    role: edge.role,
    as: edge.as,
  })) || [];

  const similarRecordings = song?.recordings || [];

  const handleSongClick = (songId?: string) => {
    if (songId) {
      router.push(`/song/?id=${songId}`);
    }
  };

  const handlePlayClick = () => {
    if (!rec) return;
    if (currentSong?.id === rec.mbid) {
      togglePlay();
    } else {
      playSong({
        id: rec.mbid || "",
        title: rec.title ?? "",
        artist: (performedBy?.[0]?.name || performedBy?.[0]?.artistName) ?? "Unknown Artist",
        album: "",
        albumArt: rec.coverImage ?? "/dummy-image.png",
        duration: rec.duration ?? 0,
        audioSrc: "",
        credits: {} as { producer?: string; writer?: string; engineer?: string },
      });
    }
  };

  const handleArtistClick = (artistId?: string) => {
    if (artistId) {
      router.push(`/artist/?id=${artistId}`);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="px-4 md:px-8 lg:px-16 py-8">
          {/* Header / Hero Section Skeleton */}
          <div className="flex flex-col lg:flex-row gap-8 mb-8">
            {/* Recording Cover Skeleton */}
            <div className="flex-shrink-0">
              <div className="w-[300px] h-[300px] bg-muted rounded-lg shadow-lg animate-pulse" />
            </div>
            {/* Recording Info Skeleton */}
            <div className="flex-1 space-y-6">
              <div className="space-y-4">
                <div className="h-12 w-2/3 bg-muted rounded animate-pulse" />
                <div className="flex flex-wrap items-center gap-2">
                  <div className="h-7 w-10 bg-muted rounded animate-pulse" />
                  <div className="h-7 w-32 bg-muted rounded animate-pulse" />
                </div>
              </div>
              {/* Action Buttons Skeleton */}
              <div className="flex flex-wrap gap-3">
                <div className="h-12 w-28 bg-muted rounded animate-pulse" />
                <div className="h-12 w-28 bg-muted rounded animate-pulse" />
              </div>
              {/* Metadata Skeleton */}
              <div className="flex flex-wrap items-center gap-6">
                <div className="h-6 w-20 bg-muted rounded animate-pulse" />
                <div className="h-6 w-24 bg-muted rounded animate-pulse" />
                <div className="h-6 w-28 bg-muted rounded animate-pulse" />
              </div>
            </div>
          </div>

          {/* Song Info Block Skeleton */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Music className="w-5 h-5" />
                <span className="h-6 w-24 bg-muted rounded animate-pulse inline-block" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-6 items-center">
                <div className="w-[120px] h-[120px] bg-muted rounded shadow animate-pulse" />
                <div className="flex-1 space-y-3">
                  <div className="h-8 w-40 bg-muted rounded animate-pulse" />
                  <div className="flex flex-wrap gap-4 mt-2">
                    <div className="h-5 w-20 bg-muted rounded animate-pulse" />
                    <div className="h-5 w-24 bg-muted rounded animate-pulse" />
                    <div className="h-5 w-28 bg-muted rounded animate-pulse" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Similar Recordings and Credits Skeleton */}
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Similar Recordings Skeleton */}
            <Card className="flex-1">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold flex items-center gap-2">
                  <Music className="w-5 h-5" />
                  <span className="h-6 w-32 bg-muted rounded animate-pulse inline-block" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 h-[25rem] overflow-y-auto">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center gap-6 py-2">
                    <div className="h-5 w-40 bg-muted rounded animate-pulse" />
                    <div className="h-5 w-20 bg-muted rounded animate-pulse" />
                    <div className="h-5 w-24 bg-muted rounded animate-pulse" />
                  </div>
                ))}
              </CardContent>
            </Card>
            {/* Credits Skeleton */}
            <Card className="flex-1">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  <span className="h-6 w-24 bg-muted rounded animate-pulse inline-block" />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 h-[25rem] overflow-y-auto">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between py-2">
                    <div className="h-5 w-32 bg-muted rounded animate-pulse" />
                    <div className="h-8 w-8 bg-muted rounded-full animate-pulse" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }
  if (error) {
    // Handle specific GraphQL errors more gracefully
    if (error.message.includes("Cannot return null for non-nullable field")) {
      return (
        <div className="p-8 text-center">
          <div className="max-w-md mx-auto">
            <Music className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Recording Data Incomplete</h2>
            <p className="text-muted-foreground mb-4">
              This recording exists but some required information is missing from our database.
            </p>
            <Button onClick={() => router.back()} variant="outline">
              Go Back
            </Button>
          </div>
        </div>
      );
    }
    return (
      <div className="p-8 text-center">
        <div className="max-w-md mx-auto">
          <Music className="w-16 h-16 text-destructive mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Recording</h2>
          <p className="text-muted-foreground mb-4">
            {error.message || "Failed to load recording. Please try again later."}
          </p>
          <Button onClick={() => router.back()} variant="outline">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  if (!rec) {
    return (
      <div className="p-8 text-center">
        <div className="max-w-md mx-auto">
          <Music className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Recording Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The recording you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          <Button onClick={() => router.back()} variant="outline">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header / Hero Section */}
        <div className="flex flex-col lg:flex-row gap-8 mb-8">
          {/* Recording Cover */}
          <div className="flex-shrink-0">
            <Image
              src={rec?.coverImage ?? "/dummy-image.png"}
              alt="Recording Cover"
              width={300}
              height={300}
              className="rounded-lg shadow-lg"
            />
          </div>

          {/* Recording Info */}
          <div className="flex-1 space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl font-bold">{rec?.title ?? "Unknown Recording"}</h1>
             {uniquePerformedBy.length > 0  && <div className="flex flex-wrap items-center gap-2">
                <span className="text-xl text-muted-foreground">by</span>
                {
                  uniquePerformedBy.map((artist, i) => artist && (
                    <span
                      key={`${artist.mbid}${i}`}
                      className="text-xl text-primary cursor-pointer hover:underline font-medium"
                      onClick={() => handleArtistClick(artist.mbid)}
                      title={`${artist.role || 'Artist'}${artist.as ? ` (as ${artist.as})` : ''}`}
                    >
                      {artist.name || artist.artistName}{uniquePerformedBy.length >= 1 && i !== uniquePerformedBy.length - 1 && ","}
                    </span>
                  ))
                }
              </div>}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <Button size="lg" className="gap-2" onClick={handlePlayClick}>
                {currentSong?.id === rec?.id && isPlaying ? (
                  <>
                    <Pause className="w-5 h-5" />
                    Pause
                  </>
                ) : (
                  <>
                    <Play className="w-5 h-5" />
                    Play
                  </>
                )}
              </Button>
            </div>

            {/* Recording Metadata */}
            <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>
                  {rec?.duration
                    ? `${Math.floor((rec.duration ?? 0) / 60000)}:${String(Math.floor(((rec.duration ?? 0) % 60000) / 1000)).padStart(2, "0")}`
                    : "N/A"}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{rec?.releaseDate ?? "Unknown Date"}</span>
              </div>
              {rec?.recordID && (
                <div className="flex items-center gap-2">
                  <Music className="w-4 h-4" />
                  <span>ISRC: {rec?.recordID ?? "N/A"}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Song Info Block */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Music className="w-5 h-5" />
              Song Info
            </CardTitle>
          </CardHeader>
          <CardContent>
            {song ? (
              <div className="flex flex-col md:flex-row gap-6 items-center">
                <Image
                  src={song.coverImage ?? "/dummy-image.png"}
                  alt="Song Cover"
                  width={120}
                  height={120}
                  className="rounded shadow"
                />
                <div>
                  <div className="text-2xl font-semibold cursor-pointer" onClick={() => handleSongClick(song.mbid)}>{song.title ?? "Unknown Song"}</div>
                  <div className="flex flex-wrap gap-4 mt-2 text-muted-foreground">
                    <div>
                      <Clock className="inline w-4 h-4 mr-1" />
                      {song.duration
                        ? `${Math.floor((song.duration ?? 0) / 60000)}:${String(Math.floor(((song.duration ?? 0) % 60000) / 1000)).padStart(2, "0")}`
                        : "N/A"}
                    </div>
                    <div>
                      <Calendar className="inline w-4 h-4 mr-1" />
                      {song.releaseDate ?? "Unknown Date"}
                    </div>
                    {song.recordID && (
                      <div>
                        <Music className="inline w-4 h-4 mr-1" />
                        ISRC: {song.recordID ?? "N/A"}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Music className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                <p className="text-muted-foreground">
                  No song information available for this recording.
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  This recording may be a standalone track or part of an unreleased work.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Similar Recordings and Recording Credits Row/Column Responsive */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Similar Recordings */}
          {/* You can add Similar Recordings Card here if needed */}

          {/* Recording Credits */}
          <Card className="flex-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5" />
                Recording Credits
              </CardTitle>
            </CardHeader>
            <CardContent
              className={
                rec?.creditedOnRecordingConnection?.edges && rec.creditedOnRecordingConnection.edges.length > 0
                  ? "space-y-4 max-h-[25rem] overflow-y-auto"
                  : "flex flex-col justify-center items-center min-h-[12rem]"
              }
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                {rec?.creditedOnRecordingConnection?.edges && rec.creditedOnRecordingConnection.edges.length > 0 ? (
                  (() => {
                    // Group credits by person name/as/artistName
                    const grouped: Record<string, Array<{
  role: string;
  as?: string;
  node: {
    mbid?: string;
    name?: string;
    artistName?: string;
    profileImage?: string;
    links?: string;
  };
}>> = {};
                    rec.creditedOnRecordingConnection.edges.forEach((credit: {
                      role: string;
                      as?: string;
                      node: {
                        mbid?: string;
                        name?: string;
                        artistName?: string;
                        profileImage?: string;
                        links?: string;
                      };
                    }) => {
                      const node = credit.node || {};
                      const personKey = credit.as || node.name || node.artistName || "Unknown";
                      if (!grouped[personKey]) grouped[personKey] = [];
                      grouped[personKey].push(credit);
                    });
                    return Object.entries(grouped).map(([person, credits], idx) => {
                      const firstCredit = credits[0];
                      const node = firstCredit.node || {};
                      let linksObj: Record<string, string[]> = {};
                      try {
                        linksObj = node.links ? JSON.parse(node.links) : {};
                      } catch {}
                      return (
                        <div key={`${node.mbid ?? person}${idx}`} className="flex items-start gap-4 p-2 rounded-lg">
                          {node.profileImage ? (
                            <Image
                              src={node.profileImage.trim()}
                              alt={node.name || node.artistName || "Artist"}
                              width={48}
                              height={48}
                              className="rounded-full object-cover border"
                            />
                          ) : (
                            <Avatar className="w-12 h-12">
                              <AvatarFallback className="text-sm font-medium">
                                {(node.name || node.artistName || "Artist")
                                  .split(' ')
                                  .map(word => word.charAt(0).toUpperCase())
                                  .slice(0, 2)
                                  .join('')}
                              </AvatarFallback>
                            </Avatar>
                          )}
                          <div className="flex-1">
                            <p
                              className="font-medium text-primary cursor-pointer hover:underline"
                              onClick={() => {
                                if (node.mbid) router.push(`/artist/?id=${node.mbid}`);
                              }}
                            >
                              {person}
                            </p>
                            <ul className="text-sm text-muted-foreground capitalize list-disc ml-4">
                              {credits.map((credit, i) => (
                                <li key={credit.role + i}>{credit.role}</li>
                              ))}
                            </ul>
                            {linksObj && Object.keys(linksObj).length > 0 && (
                              <div className="flex flex-col gap-1 mt-1">
                                {Object.entries(linksObj).map(([platform, urls]) => (
                                  <div key={platform} className="flex items-center gap-2">
                                    {(urls as string[]).map((url, i) => (
                                      <a
                                        key={platform + i}
                                        href={url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="hover:opacity-80"
                                      >
                                        {/* Platform icon or globe can be added here */}
                                      </a>
                                    ))}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    });
                  })()
                ) : (
                  <div className="text-muted-foreground text-sm text-center w-full">
                    No credits available
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Song Credits */}
          <Card className="flex-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5" />
                Song Credits
              </CardTitle>
            </CardHeader>
            <CardContent
              className={
                songCredits && songCredits.length > 0
                  ? "space-y-4 max-h-[25rem] overflow-y-auto"
                  : "flex flex-col justify-center items-center min-h-[12rem]"
              }
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
                {songCredits && songCredits.length > 0 ? (
                  (() => {
                    // Group song credits by person name/as/artistName
                    const grouped: Record<string, Array<{
            role: string;
            as?: string;
            node: {
              mbid?: string;
              name?: string;
              artistName?: string;
              profileImage?: string;
              links?: string;
            };
          }>> = {};
                    songCredits.forEach((credit: {
            role: string;
            as?: string;
            node: {
              mbid?: string;
              name?: string;
              artistName?: string;
              profileImage?: string;
              links?: string;
            };
          }) => {
            const node = credit.node || {};
            const personKey = credit.as || node.name || node.artistName || "Unknown";
            if (!grouped[personKey]) grouped[personKey] = [];
            grouped[personKey].push(credit);
          });
          return Object.entries(grouped).map(([person, credits], idx) => {
            const firstCredit = credits[0];
            const node = firstCredit.node || {};
            let linksObj: Record<string, string[]> = {};
            try {
              linksObj = node.links ? JSON.parse(node.links) : {};
            } catch {}
            return (
              <div key={`${node.mbid ?? person}${idx}`} className="flex items-start gap-4 p-2  rounded-lg">
                {node.profileImage ? (
                  <Image
                    src={node.profileImage.trim()}
                    alt={node.name || node.artistName || "Artist"}
                    width={48}
                    height={48}
                    className="rounded-full object-cover border"
                  />
                ) : (
                  <Avatar className="w-12 h-12">
                    <AvatarFallback className="text-sm font-medium">
                      {(node.name || node.artistName || "Artist")
                        .split(' ')
                        .map(word => word.charAt(0).toUpperCase())
                        .slice(0, 2)
                        .join('')}
                    </AvatarFallback>
                  </Avatar>
                )}
                <div className="flex-1">
                  <p
                    className="font-medium text-primary cursor-pointer hover:underline"
                    onClick={() => {
                      if (node.mbid) router.push(`/artist/?id=${node.mbid}`);
                    }}
                  >
                    {person}
                  </p>
                  <ul className="text-sm text-muted-foreground capitalize list-disc ml-4">
                    {credits.map((credit, i) => (
                      <li key={credit.role + i}>{credit.role}</li>
                    ))}
                  </ul>
                  {linksObj && Object.keys(linksObj).length > 0 && (
                    <div className="flex flex-col gap-1 mt-1">
                      {Object.entries(linksObj).map(([platform, urls]) => (
                        <div key={platform} className="flex items-center gap-2">
                          {(urls as string[]).map((url, i) => (
                            <a
                              key={platform + i}
                              href={url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:opacity-80"
                            >
                              {/* Platform icon or globe can be added here */}
                            </a>
                          ))}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          });
        })()
      ) : (
        <div className="text-muted-foreground text-sm text-center w-full">
          No credits available
        </div>
      )}
    </div>
  </CardContent>
</Card>
        </div>

        {/* similar recordings*/}
        <Card className="flex-1 mt-8">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold flex items-center gap-2">
              <Music className="w-5 h-5" />
              Similar Recordings
            </CardTitle>
          </CardHeader>
          <CardContent
            className={
              similarRecordings && similarRecordings.length > 0
                ? "space-y-6"
                : "flex flex-col justify-center items-center min-h-[12rem]"
            }
          >
            {similarRecordings && similarRecordings.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Release Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {similarRecordings.map((rec: {
                    mbid?: string;
                    title?: string;
                    duration?: number;
                    releaseDate?: string;
                  }) => (
                    <TableRow key={rec?.mbid}>
                      <TableCell onClick={() => rec?.mbid && router.push(`/recording/?id=${rec.mbid}`)} className="cursor-pointer">{rec?.title ?? "Untitled"}</TableCell>
                      <TableCell>
                        {rec?.duration
                          ? `${Math.floor((rec.duration ?? 0) / 60000)}:${String(Math.floor(((rec.duration ?? 0) % 60000) / 1000)).padStart(2, "0")}`
                          : "N/A"}
                      </TableCell>
                      <TableCell>{rec?.releaseDate ?? "N/A"}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center w-full">
                <Music className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No similar recordings available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <MusicPlayer />
    </div>
  );
}