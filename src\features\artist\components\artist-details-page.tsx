"use client";
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Calendar,
  Edit3,
  UserPlus,
  LinkIcon,
  Award,
  X
} from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import { useEffect, useState, useMemo } from "react"
import { ARTIST_PROFILE_QUERY } from "@/graphql/queries"
import { Accordion, AccordionContent, AccordionTrigger } from "@/components/ui/accordion";
import { AccordionItem } from "@radix-ui/react-accordion";
import { useQuery } from '@apollo/client';


export default function ArtistDetailsPage({ artistId }: { artistId?: string }) {
  const router = useRouter();
  const params = useParams();
  const idFromParams = params?.id as string | undefined;
  const effectiveArtistId = artistId || idFromParams;

  // Apollo Client query
  const { data: apolloData, loading, error } = useQuery(ARTIST_PROFILE_QUERY, {
    variables: { artistId: effectiveArtistId },
    skip: !effectiveArtistId, // Skip query if no artistId
  });

  const [categorizedSongs, setCategorizedSongs] = useState<Record<string, Array<{ id: string; title?: string | null; duration?: number | null; releaseDate?: string | null }>>>({});
  const [openRoles, setOpenRoles] = useState<string[]>([]);

  // Process Apollo data
  const artistConnection = apolloData?.artistsConnection || null;
  const artist = artistConnection?.edges?.[0]?.node || null;

  // Process songs from creditedOnSongConnection using useMemo to prevent infinite re-renders
  const songs = useMemo(() => {
    const songEdges = artist?.creditedOnSongConnection?.edges || [];

    return songEdges.map((edge: {
      role?: string;
      as?: string;
      node?: {
        mbid?: string; // Song's mbid
        title?: string;
        coverImage?: string;
        recordings?: Array<{
          mbid?: string;
          title?: string;
          duration?: number;
          recordID?: string;
          releaseDate?: string;
          coverImage?: string;
        }>;
        creditedOnSong?: Array<{
          mbid?: string;
          name?: string;
          artistName?: string;
        }>;
      };
    }) => {
      const songMbid = edge.node?.mbid;

      // Use song's mbid as the correct ID
      const idToUse = songMbid || Math.random().toString();

      return {
        id: idToUse,
        title: edge.node?.title || null,
        duration: edge.node?.recordings?.[0]?.duration || null,
        recordID: edge.node?.recordings?.[0]?.recordID || null,
        releaseDate: edge.node?.recordings?.[0]?.releaseDate || null,
        coverPhoto: edge.node?.coverImage || edge.node?.recordings?.[0]?.coverImage || null,
        role: edge.role || null,
        credits: [{
          artistId: artist?.mbid || '',
          role: edge.role || '',
          name: artist?.name || artist?.artistName || null,
        }]
      };
    });
  }, [artist]);

  // Process albums data
  const albums = useMemo(() => {
    return (artist?.albums || []).map((album: {
      mbid?: string;
      title?: string;
      releaseDate?: string;
      coverArtData?: string;
    }) => ({
      id: album.mbid || Math.random().toString(),
      title: album.title || null,
      releaseDate: album.releaseDate || null,
      coverArtData: album.coverArtData || null,
    }));
  }, [artist]);

  /**
 * Categorizes songs by the given artist's roles.
 * @param songs Array of song objects
 * @param artistId The artist's ID to filter by
 * @returns Object mapping role to array of songs
 */
function categorizeSongsByRole(
  songs: Array<{
    id: string;
    title?: string | null;
    duration?: number | null;
    releaseDate?: string | null;
    credits?: Array<{
      artistId: string;
      role: string;
      name?: string | null;
    }>;
  }> = [],
  artistId: string
) {
  const result: Record<string, Array<{ id: string; title?: string | null; duration?: number | null; releaseDate?: string | null }>> = {};

  songs.forEach(song => {
    if (!song.credits) return;
    song.credits.forEach(credit => {
      if (credit.artistId === artistId && credit.role) {
        if (!result[credit.role]) {
          result[credit.role] = [];
        }
        result[credit.role].push({
          id: song.id,
          title: song.title,
          duration: song.duration,
          releaseDate: song.releaseDate,
        });
      }
    });
  });

  return result;
}


  useEffect(() => {
    if (!songs || !artist?.mbid) return;

    const categorized = categorizeSongsByRole(songs, artist.mbid);
    setCategorizedSongs(categorized);

    // Open all accordions by default
    const allRoles = Object.keys(categorized);
    setOpenRoles(['discography', ...allRoles]);
  }, [songs, artist])




  const handleAlbumClick = (albumId : string) => {
    router.push(`/album/?id=${albumId}`);
  };
  const handleSongClick = (songId: string) => {
    router.push(`/song/?id=${songId}`);
  }
  const handleAccordionChange = (values: string[]) => {
    setOpenRoles(values);
  };

  const handleCloseRole = (role: string) => {
    setOpenRoles((prev) => prev.filter((item) => item !== role));
  };
  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center text-red-500">
        Error loading data: {error.message}
      </div>
    );
  }

  if (!artist) {
    return <div className="min-h-screen flex items-center justify-center text-red-500">Artist not found</div>;
  }
  
function getCoverArtUrl(coverArtData?: string | null) {
  try {
    const parsed = JSON.parse(coverArtData || "");
    const innerParsed = typeof parsed === "string" ? JSON.parse(parsed) : parsed;
  return  innerParsed.thumbnails['250'] || innerParsed.image_url || "/dummy-image.png?height=150&width=150";
  } catch {
    return "/dummy-image.png?height=150&width=150";
  }
}






  return (
    <div className="min-h-screen bg-background">
      {/* Banner Section */}
      <div className="relative h-64 md:h-80 w-full">
        <Image src="/dummy-image.png?height=320&width=1200" alt="Artist Banner" fill className="object-cover" />
        <div className="absolute inset-0 bg-black/20" />
      </div>

      {/* Profile Header */}
      <div className="relative px-4 md:px-8 lg:px-16">
        <div className="flex flex-col md:flex-row items-start md:items-end gap-4 -mt-16 md:-mt-20">
          <Avatar className="w-32 h-32 md:w-40 md:h-40 border-4 border-background relative overflow-hidden">
            {/* Blurred background */}
            <div
              className="absolute inset-0 bg-cover bg-center blur-md scale-110"
              style={{
                backgroundImage: `url(${artist.profileImage || "/dummy-image.png"})`
              }}
            />
            {/* Main image */}
            <AvatarImage
              src={artist.profileImage || "/dummy-image.png"}
              alt="Artist Profile"
              className="object-contain w-full h-full relative z-10"
            />
            <AvatarFallback className="text-2xl">{(artist.name || artist.artistName)?.[0]?.toUpperCase() ?? "A"}</AvatarFallback>
          </Avatar>

          <div className="flex-1 space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl md:text-4xl font-bold">{artist.name || artist.artistName}</h1>
              <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>Active since {artist.formedDate ? new Date(artist.formedDate).getFullYear() : "Unknown"}</span>
                </div>
                {artist.disbandedDate && (
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>Disbanded {new Date(artist.disbandedDate).getFullYear()}</span>
                  </div>
                )}
              </div>
              {/* Genres/Badges - can be updated with real data when available */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Pop</Badge>
                <Badge variant="secondary">Synthwave</Badge>
                <Badge variant="secondary">Electronic</Badge>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button>
                <UserPlus className="w-4 h-4 mr-2" />
                Follow
              </Button>
              <Button variant="outline">
                <LinkIcon className="w-4 h-4 mr-2" />
                Connect
              </Button>
              <Button variant="outline">
                <Edit3 className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 md:px-8 lg:px-16 py-8 space-y-8">
        {/* About Section */}
        <Card>
          <CardHeader>
            <CardTitle>About</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed">
              {artist.bio || "No biography available for this artist."}
            </p>
          </CardContent>
        </Card>

        {/* Experience / Discography Section */}
              <div className="mt-8">
          <Accordion
            type="multiple"
            className="w-full"
            value={openRoles}
            onValueChange={handleAccordionChange}
          >
            <AccordionItem
              value="discography"
              className="border rounded-lg bg-card shadow-sm mb-8"
            >
              <AccordionTrigger className="px-6 py-4 text-lg font-semibold flex items-center gap-2">
                <div>
                  Discography
                  <span className="ml-2 text-xs text-muted-foreground">
                    ({albums.length})
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
                  {albums.length > 0 ? (
                    albums.map((album: {
                      id: string;
                      title?: string | null;
                      releaseDate?: string | null;
                      coverArtData?: string | null;
                    }) => (
                      <div
                        key={album.id}
                        className="space-y-2 cursor-pointer"
                        onClick={() => handleAlbumClick(album.id)}
                      >
                        <div className="relative w-[150px] h-[150px] rounded-md overflow-hidden cursor-pointer">
                          {/* Blurred background */}
                          <div
                            className="absolute inset-0 bg-cover bg-center blur-md scale-110"
                            style={{
                              backgroundImage: `url(${getCoverArtUrl(album.coverArtData)})`
                            }}
                          />
                          {/* Main image */}
                          
                          <Image
                            src={getCoverArtUrl(album.coverArtData)}
                            alt={`Album ${album.title || "Unknown"}`}
                            width={150}
                            height={150}
                            className="object-contain w-full h-full relative z-10"
                          />
                        </div>
                        <p className="font-medium">{album.title || "Unknown Album"}</p>
                        <p className="text-sm text-muted-foreground">
                          {album.releaseDate
                            ? new Date(album.releaseDate).getFullYear()
                            : "Unknown Year"}
                        </p>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted-foreground col-span-full">
                      No albums available
                    </p>
                  )}
                </div>
                <div className="flex justify-end mt-2">
                  <Button
                    variant="secondary"
                    className="ml-auto"
                    onClick={e => {
                      e.stopPropagation();
                      setOpenRoles(prev => prev.filter(item => item !== "discography"));
                    }}
                  >
                    Close <X className="ml-1" />
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        {/* Credits Section */}
        <div className="mt-8">
          <div className="flex items-center gap-2 mb-4">
            <Award className="w-5 h-5" />
            <span className="text-xl font-semibold">Credits</span>
          </div>
          {Object.keys(categorizedSongs).length === 0 ? (
            <div className="italic text-muted-foreground px-4 py-2">
              {songs.length > 0
                ? `No credits found for this artist (${songs.length} songs available)`
                : "No songs available for this artist"
              }
            </div>
          ) : (
            <div className="space-y-4">
              <Accordion
                type="multiple"
                className="w-full"
                value={openRoles}
                onValueChange={handleAccordionChange}
              >
                {Object.entries(categorizedSongs).map(([role, songs]) => (
                  <AccordionItem
                    value={role}
                    key={role}
                    className="border rounded-lg bg-card shadow-sm mb-8"
                  >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold flex items-center gap-2">
                      <div>
                        {role.charAt(0).toUpperCase() + role.slice(1)}
                        <span className="ml-2 text-xs text-muted-foreground">
                          ({songs.length})
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pb-4">
                      {songs.length > 0 ? (
                        <div className="divide-y">
                          {songs.map((song) => (
                            <div
                              key={song.id}
                              className="py-3 px-2 transition-colors duration-150 cursor-pointer hover:bg-muted/30 rounded"
                              onClick={() => handleSongClick(song.id)}
                            >
                              <div className="flex items-center gap-8 font-medium">
                                <span className="flex-1">{song.title || "-"}</span>
                                <span className="text-sm flex-1 text-muted-foreground">
                                  <span className="text-sm flex-1 text-muted-foreground">
  {typeof song.duration === "number"
    ? `${Math.floor(song.duration / 1000 / 60)}:${String(Math.floor((song.duration / 1000) % 60)).padStart(2, "0")}`
    : "-"}
</span>


                                </span>
                                <span className="text-sm flex-1 text-muted-foreground">
                                  Release Date:  {song.releaseDate
                                    ? new Date(song.releaseDate).toLocaleDateString()
                                    : "-"}
                                </span>
                              </div>
                            </div>
                          ))}
                          <div className="flex justify-end mt-2">
                            <Button
                              variant="secondary"
                              className="ml-auto"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCloseRole(role);
                              }}
                            >
                              Close <X className="ml-1" />
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="italic text-muted-foreground px-4 py-2">
                          (No songs found)
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          )}
        </div>
       
      </div>
    </div>
  )
}
